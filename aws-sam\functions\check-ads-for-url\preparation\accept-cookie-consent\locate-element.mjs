/**
 * Attempts to locate an element on the page with a timeout.
 * Returns null if the element is not found within the timeout period.
 */
export async function locateElement(page, selector, options = {}) {
  const { timeout = 1000 } = options;

  try {
    return await page.waitForSelector(selector, { timeout });
  } catch {
    return null;
  }
}

/**
 * Creates a reusable locator function for a specific selector.
 * This is useful for passing to functions like simulateMouseActivity or simulateScrollActivity.
 */
export function createLocator(page, selector, options = {}) {
  return async () => locateElement(page, selector, options);
}
