name: SAM Deploy to AWS Lambda

on:
  push:
    branches:
      - main

env:
  AWS_REGION: eu-north-1

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write # Required for OIDC to assume the role
      contents: read # Required to checkout the code

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Configure AWS Credentials (OIDC)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_IAM_ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}
          disable-retry: true

      - name: Set up SAM CLI
        uses: aws-actions/setup-sam@v2

      - name: SAM Build
        run: sam build --template-file aws-sam/template.yml

      - name: SAM Deploy
        run: |
          sam deploy \
            --config-file aws-sam/samconfig.toml \
            --parameter-overrides CommitSha=${{ github.sha }}
