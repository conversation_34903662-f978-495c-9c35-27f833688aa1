/**
 * Injects scripts to block popups, overlays, and other intrusive elements
 * This runs before any page content loads to prevent popups from appearing
 * @param {Object} page - Puppeteer page instance
 */
export async function injectPopupBlocker(page) {
  await page.evaluateOnNewDocument(() => {
    const selectors = [
      '.newsletter',
      '.newsletter-popup',
      '.cleverpush-confirm',
      '.mailerlite-container',
      '.tbl-next-up',
      '[class*="overlay"]',
      '[class*="popup"]',
      '[class*="backdrop"]',
      '[id*="overlay"]',
      '[id*="modal"]',
      '[id*="popup"]',
      '[id*="backdrop"]',
    ];

    const removeMatching = () => {
      document
        .querySelectorAll(selectors.join(','))
        .forEach((el) => el.remove());
    };

    const block = (node) => {
      if (!(node instanceof Element)) return;
      if (selectors.some((sel) => node.matches(sel))) {
        node.remove();
      }
    };

    const setupObserver = () => {
      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          mutation.addedNodes.forEach(block);
        }
      });

      observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style'],
      });
    };

    // Set up observer immediately if possible
    if (document.documentElement) {
      setupObserver();
    } else {
      // Wait for document to be ready
      const checkReady = setInterval(() => {
        if (document.documentElement) {
          clearInterval(checkReady);
          setupObserver();
        }
      }, 10);
    }

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', removeMatching);
    } else {
      removeMatching();
    }

    // Remove delayed popups
    setTimeout(removeMatching, 3000);
  });
}
