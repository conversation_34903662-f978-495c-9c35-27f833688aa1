import pushIssue from './push-issue.mjs';

/**
 * Process "No container div" visibility issue
 */
export function processNoContainerDiv(
  ad,
  result,
  responsiveKey,
  visibilityDetails,
  reason,
  issues
) {
  if (reason.startsWith('No container div')) {
    pushIssue(
      {
        name: 'qmn div container not found',
        description: 'Cannot check the Ad if no qmn[div] container',
        severity: 'RED',
        step: 'Check Visibility',
        device: result.deviceName,
        responsive: responsiveKey,
        adId: ad.id,
        adType: ad.type,
        visibilityDetails,
      },
      issues
    );
    return true;
  }
  return false;
}
