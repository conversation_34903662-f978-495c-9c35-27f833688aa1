import { tryTraditionalCookieConsent } from './try-traditional-cookie-consent.mjs';
import { tryShadowDomCookieConsent } from './try-shadow-dom-cookie-consent.mjs';
import { tryIframeCookieConsent } from './try-iframe-cookie-consent.mjs';
import { logger } from '../../utils/logger.mjs';

export const acceptCookieConsent = async (page) => {
  try {
    logger.silly('Attempting to accept cookie consent...');

    // Try shadow DOM approach
    const shadowResult = await tryShadowDomCookieConsent(page);
    if (shadowResult === 1) {
      return 1; // Success
    }

    logger.silly(
      'Shadow DOM approach did not succeed, trying traditional approach...'
    );

    // Fall back to traditional approach
    const traditionalResult = await tryTraditionalCookieConsent(page);
    if (traditionalResult === 1) {
      return 1; // Success
    }

    logger.silly(
      'Traditional approach did not succeed, trying iframe approach...'
    );

    // Try iframe approach
    const iframeResult = await tryIframeCookieConsent(page);
    return iframeResult;
  } catch (err) {
    logger.silly('⚠️ Error during cookie consent:', err.message);
    return -1;
  }
};
