import pushIssue from './push-issue.mjs';
import severityFromPercentage from './severity-from-percentage.mjs';

/**
 * Process "Occluded" visibility issue
 */
export function processOccluded(
  ad,
  result,
  responsiveKey,
  visibilityDetails,
  vis,
  issues
) {
  if (visibilityDetails.isOccluded) {
    const sev = severityFromPercentage(vis);
    if (sev) {
      pushIssue(
        {
          name: 'Ad is covered',
          description: 'Ad covered by an other element',
          visibilityPercentage: vis,
          severity: sev,
          step: 'Check Visibility',
          device: result.deviceName,
          responsive: responsiveKey,
          adId: ad.id,
          adType: ad.type,
          occluders: visibilityDetails.occludingElements,
          visibilityDetails,
        },
        issues
      );
      return true;
    }
  }
  return false;
}
