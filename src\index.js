import { Worker } from 'bullmq';
import IORedis from 'ioredis';
import { handler } from './handler.js';

// Prevent Logging on DO of all reconnection to Redis
let isAlreadyConnected = false;
const concurrency = parseInt(process.env.BULLMQ_CONCURRENCY) || 1;

const connection = new IORedis({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT, 10),
  username: process.env.REDIS_USERNAME,
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: null,
  tls: process.env.NODE_ENV === 'production' ? {} : undefined,
});

console.group();
console.log('****************** SETTINGS ******************');
console.log(`*  Redis`);
console.log(`*  - host: ${process.env.REDIS_HOST}`);
console.log(`*  - port: ${process.env.REDIS_PORT}`);
console.log(`*  - username: ${process.env.REDIS_USERNAME}`);
console.log(`*`);
console.log(`*  BullMQ`);
console.log(`*  - concurrency: ${concurrency}`);
console.log(`*`);
console.log(`*  Global`);
console.log(`*  - node_env: ${process.env.NODE_ENV}`);
console.log('**********************************************');
console.groupEnd();

connection.on('error', (err) => {
  console.error('⛔️ Redis connection error:', err);
});

connection.on('connect', () => {
  if (isAlreadyConnected) return;

  console.log('✅ Successfully connected to Redis!\n');
  isAlreadyConnected = true;
});

const worker = new Worker('ad-checker-scheduled', handler, {
  connection,
  concurrency,
});

worker.on('error', (err) => {
  console.error('Worker error:', err);
});
worker.on('failed', (job, err) => {
  console.error(`Job failed: ${err.message}`, { jobId: job.id });
});
