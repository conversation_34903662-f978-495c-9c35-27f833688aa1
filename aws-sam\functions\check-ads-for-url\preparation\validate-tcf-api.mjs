/**
 * Validates that the TCF API is available and returns success
 * @param {Object} page - Puppeteer page instance
 * @returns {Promise} Resolves when TCF API is confirmed, rejects on timeout/error
 */
export function validateTcfApi(page) {
  return page.evaluate(() => {
    return new Promise((resolve, reject) => {
      // Retry mechanism to wait for the TCF API to be ready and successful
      const checkTcfApi = (retries = 3) => {
        // ~1.5 seconds timeout (3 * 500ms)
        if (typeof window.__tcfapi === 'function') {
          window.__tcfapi('getTCData', 2, (tcData, success) => {
            if (success) {
              console.log('TCF API success: ✅ ');
              resolve();
            } else if (retries > 0) {
              setTimeout(() => checkTcfApi(retries - 1), 500);
            } else {
              reject(
                new Error('TCF API did not return success within the timeout.')
              );
            }
          });
        } else if (retries > 0) {
          setTimeout(() => checkTcfApi(retries - 1), 500);
        } else {
          reject(
            new Error(
              '__tcfapi function not found on page within the timeout. ❌ '
            )
          );
        }
      };
      checkTcfApi();
    });
  });
}
