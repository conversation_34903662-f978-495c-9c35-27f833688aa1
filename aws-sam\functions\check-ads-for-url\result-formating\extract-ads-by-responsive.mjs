/**
 * Extracts adsByResponsive from device results
 * Checks multiple device results in order and returns the first valid one
 * @param {Array} deviceResults - Array of device result objects
 * @returns {Object} adsByResponsive object or empty object if none found
 */
export function extractAdsByResponsive(deviceResults) {
  let adsByResponsive = {};

  // Check indices 0, 1, 2 before falling back to empty object
  if (deviceResults.length > 0 && deviceResults[0]?.adsByResponsive) {
    adsByResponsive = deviceResults[0].adsByResponsive;
  } else if (deviceResults.length > 1 && deviceResults[1]?.adsByResponsive) {
    adsByResponsive = deviceResults[1].adsByResponsive;
  } else if (deviceResults.length > 2 && deviceResults[2]?.adsByResponsive) {
    adsByResponsive = deviceResults[2].adsByResponsive;
  }

  return adsByResponsive;
}
