import { logger } from '../../utils/logger.mjs';

/**
 * Waits for an element's bounding box to stabilize by checking if its position
 * remains constant across multiple checks. This is useful for waiting for animations
 * or transitions to complete before interacting with the element.
 */
export async function waitForElementStable(element, options = {}) {
  const { maxAttempts = 10, checkInterval = 100, threshold = 1 } = options;

  logger.silly('Waiting for element animation to stabilize...');

  let stableBox = null;
  let attempts = 0;

  while (attempts < maxAttempts) {
    const box1 = await element.boundingBox();
    await new Promise((resolve) => setTimeout(resolve, checkInterval));
    const box2 = await element.boundingBox();

    // Check if position is stable and in viewport
    if (
      box1 &&
      box2 &&
      Math.abs(box1.y - box2.y) < threshold &&
      Math.abs(box1.x - box2.x) < threshold &&
      box2.y >= 0
    ) {
      stableBox = box2;
      logger.silly(
        `Element stabilized after ${(attempts + 1) * checkInterval}ms at y=${box2.y}`
      );
      break;
    }

    attempts++;
  }

  if (!stableBox) {
    logger.silly(
      `Element did not stabilize within ${maxAttempts * checkInterval}ms`
    );
  }

  return stableBox;
}
