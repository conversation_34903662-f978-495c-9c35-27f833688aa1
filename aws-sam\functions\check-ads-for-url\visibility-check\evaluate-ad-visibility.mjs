import { logger } from '../utils/logger.mjs';
import { checkVisibilityLogic } from './check-visibility-logic.mjs';

/**
 * Evaluates the visibility of an ad and builds detailed result object
 * @param {Object} page - Puppeteer page instance
 * @param {string} adSelector - CSS selector for the ad element
 * @param {Object} ad - Ad object with id and type
 * @param {string} deviceName - Device name for logging
 * @returns {Promise<Object>} Object with visibilityDetails or null if visible
 */
export async function evaluateAdVisibility(page, adSelector, ad, deviceName) {
  // Evaluate the visibility of this single, now centered element.
  const visibilityResults = await page.evaluate(
    checkVisibilityLogic,
    adSelector
  );
  const result = visibilityResults[0];

  if (!result || result.visibilityPercentage < 100) {
    let reason = 'Others'; // Default reason

    if (result.reason === 'Element not found') {
      reason = 'Element not found';
    } else if (!result.isVisibleByCss) {
      reason = 'Hidden by CSS';
    } else if (result.isOccluded) {
      reason = 'Covered';
    } else if (result.isPartiallyOutOfViewport) {
      reason = 'Not in viewport';
    } else if (!result.isWithinPageBounds) {
      reason = 'Not within page bounds';
    }

    const details = {
      visibilityPercentage: result.visibilityPercentage,
      reason: reason,
      isVisibleByCss: result.isVisibleByCss,
      isOccluded: result.isOccluded,
      isPartiallyOutOfViewport: result.isPartiallyOutOfViewport,
      isWithinPageBounds: result.isWithinPageBounds,
      occludingElements: result.occludingElements,
      debugInfo: result.debugInfo,
    };

    logger.silly(
      `### ➡️ [${deviceName}] - ❌ ${adSelector}: Not visible. Reason: ${details.reason}, Visibility: ${details.visibilityPercentage}%`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - ❌ ${adSelector}: Not visible. Reason: ${details.reason}, Visibility: ${details.visibilityPercentage}%`
    );

    return {
      id: ad.id,
      type: ad.type,
      visibilityDetails: details,
    };
  } else {
    logger.silly(
      `### ➡️ [${deviceName}] - ✅ ${adSelector}: Visible. (Visibility: ${result.visibilityPercentage}%)`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - ✅ ${adSelector}: Visible. (Visibility: ${result.visibilityPercentage}%)`
    );

    return null; // Ad is visible, no issue
  }
}
