import { logger } from '../utils/logger.mjs';

export async function findSidebarVisibilityPosition(
  page,
  elementHandle,
  startScrollY,
  scrollAmount,
  direction,
  maxAttempts,
  deviceName,
  adId,
  scrollInfo
) {
  let attempts = 0;
  let currentScrollY = startScrollY;

  while (attempts < maxAttempts) {
    // Scroll by the specified amount in the given direction
    currentScrollY += direction * scrollAmount;

    // Ensure we don't scroll beyond page bounds
    if (currentScrollY < 0) {
      currentScrollY = 0;
    } else if (currentScrollY > scrollInfo.maxScroll) {
      currentScrollY = scrollInfo.maxScroll;
    }

    await page.evaluate((scrollY) => {
      window.scrollTo(0, scrollY);
    }, currentScrollY);
    await new Promise((resolve) => setTimeout(resolve, 150)); // Give CSS time to update

    // Check if sidebar is now CSS visible at this scroll position
    const isCssVisible = await elementHandle.evaluate((el) => {
      if (typeof el.checkVisibility !== 'function') return false;
      return el.checkVisibility({
        checkOpacity: true,
        checkVisibilityCSS: true,
      });
    });

    if (isCssVisible) {
      logger.silly(
        `### ➡️ [${deviceName}] - Found sidebar visibility at scroll position ${currentScrollY} after ${attempts + 1} attempts`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - Found sidebar visibility at scroll position ${currentScrollY} after ${attempts + 1} attempts`
      );
      return true;
    }

    attempts++;

    // Check if we've reached page boundaries
    if (
      (direction === -1 && currentScrollY === 0) ||
      (direction === 1 && currentScrollY >= scrollInfo.maxScroll)
    ) {
      logger.silly(
        `### ➡️ [${deviceName}] - Reached page ${direction === 1 ? 'end' : 'start'} at scroll ${currentScrollY} without finding visible position for sidebar ad ${adId}`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - Reached page ${direction === 1 ? 'end' : 'start'} at scroll ${currentScrollY} without finding visible position for sidebar ad ${adId}`
      );
      break;
    }
  }

  logger.silly(
    `### ➡️ [${deviceName}] - Could not find visible position for sidebar ad ${adId} after ${attempts} attempts in ${direction === 1 ? 'downward' : 'upward'} direction`
  );
  console.log(
    `### ➡️ [${deviceName}] [VISIBILITY] - Could not find visible position for sidebar ad ${adId} after ${attempts} attempts in ${direction === 1 ? 'downward' : 'upward'} direction`
  );
  return false;
}
