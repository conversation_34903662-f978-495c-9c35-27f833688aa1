import pushIssue from './push-issue.mjs';
import severityFromPercentage from './severity-from-percentage.mjs';

/**
 * Process "Partially out of viewport" visibility issue
 */
export function processPartiallyOutOfViewport(
  ad,
  result,
  responsiveKey,
  visibilityDetails,
  vis,
  issues
) {
  if (visibilityDetails.isPartiallyOutOfViewport) {
    const sev = severityFromPercentage(vis);
    if (sev) {
      pushIssue(
        {
          name: 'Ad not fully in viewport',
          description: 'Partially / completely out of viewport',
          visibilityPercentage: vis,
          severity: sev,
          step: 'Check Visibility',
          device: result.deviceName,
          responsive: responsiveKey,
          adId: ad.id,
          adType: ad.type,
          visibilityDetails,
        },
        issues
      );
      return true;
    }
  }
  return false;
}
