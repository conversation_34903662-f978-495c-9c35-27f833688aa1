import { logger } from '../../utils/logger.mjs';
import { calculateClickPoints } from './calculate-click-points.mjs';
import { verifyAndClickElement } from './verify-and-click-element.mjs';
import { waitForElementStable } from './wait-for-element-stable.mjs';

/**
 * Attempts to click one of the provided iframe consent buttons.
 * Returns 1 when a button was clicked, 0 when click verification failed,
 * and null when no matching button was found.
 */
export async function handleIframeConsentButtons({
  page,
  frame,
  container,
  config,
}) {
  if (!config?.buttonSelectors?.length) {
    return null;
  }

  for (const buttonSelector of config.buttonSelectors) {
    try {
      logger.silly(`Looking for button: ${buttonSelector}`);

      let button;
      if (container) {
        button = await container.waitForSelector(buttonSelector, {
          timeout: 3000,
        });
      } else {
        button = await frame.waitForSelector(buttonSelector, { timeout: 3000 });
      }

      if (!button) {
        continue;
      }

      logger.silly(`Found button: ${buttonSelector}`);

      const stableBox = await waitForElementStable(button);
      if (!stableBox) {
        logger.silly('<PERSON><PERSON> did not stabilize within timeout, skipping...');
        continue;
      }

      const box = stableBox;
      if (box.y < 0 || box.x < 0) {
        logger.silly(
          `Button is outside viewport (x=${box.x}, y=${box.y}), skipping...`
        );
        continue;
      }

      const clickPoints = calculateClickPoints(box);
      for (const point of clickPoints) {
        const clicked = await verifyAndClickElement(
          page,
          button,
          point,
          config.name,
          null,
          frame
        );
        if (clicked) {
          logger.silly('✅ Successfully clicked iframe consent button');
          return 1;
        }
      }

      logger.silly('Unable to verify safe click coordinate');
      return 0;
    } catch (err) {
      logger.silly(
        `Error with button ${buttonSelector}: ${err.message}`,
        err.message
      );
    }
  }

  return null;
}
