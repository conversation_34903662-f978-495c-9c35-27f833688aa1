import calculateProblemPercentage from './calculate-problem-percentage.mjs';

/**
 * Builds the final result object for the ad check job
 * @param {Array} issues - Array of issues found during checks
 * @param {Array} iframeSources - Array of iframe sources collected from devices
 * @param {Object} adsByResponsive - Ads grouped by responsive type
 * @param {number} durationMs - Processing time in milliseconds
 * @param {Array} activeLayers - Array of active check layers
 * @param {Object} adSummary - Ad summary by device and type
 * @param {Object} timingBreakdown - Timing breakdown per device
 * @param {Object} qmnData - Complete QMN data object with config.adSlots regrouped by device
 * @returns {Object} Final result object
 */
export function buildFinalResult(
  issues,
  iframeSources,
  sourceCommit,
  adsByResponsive,
  durationMs,
  activeLayers,
  adSummary,
  timingBreakdown,
  qmnData
) {
  const problemPercentage = calculateProblemPercentage(issues, adsByResponsive);

  return {
    success: true,
    checkedLayers: activeLayers,
    timestamp: new Date().toISOString(),
    processingTimeMs: durationMs,
    issues,
    sourceCommit,
    problemPercentage: problemPercentage,
    /* iframeSources: iframeSources, */
    adSummary: adSummary,
    timingBreakdown: timingBreakdown,
    qmnData: qmnData,
  };
}
