import { acceptCookieConsent } from './accept-cookie-consent/accept-cookie-consent.mjs';
import { logger } from '../utils/logger.mjs';

/**
 * Navigates to URL and handles cookie consent if not already handled
 * @param {Object} page - Puppeteer page instance
 * @param {string} url - URL to navigate to
 * @param {Object} config - Device configuration with name, width, height
 * @param {Object} consentState - Shared state object to track if consent was handled
 * @throws {Error} If cookie consent cannot be accepted
 */
export async function navigateAndHandleConsent(
  page,
  url,
  config,
  consentState
) {
  logger.silly(
    `\n--- Test device: ${config.name} (${config.width}x${config.height}) ---`
  );
  console.log(`
    \n--- Test device: ${config.name} (${config.width}x${config.height}) ---
    `);

  console.time(`⏳ navigate to url`);

  await page.setViewport({ width: config.width, height: config.height });
  await page.goto(url, { waitUntil: 'networkidle2', timeout: 90000 });

  console.timeEnd(`⏳ navigate to url`);

  console.time(`⏳ accept cookie consent`);
  if (!consentState.handled) {
    logger.silly(
      `[${config.name}] First device check: Trying to accept cookie consent...`
    );
    console.log(
      `[${config.name}] First device check: Trying to accept cookie consent...`
    );
    const consentResult = await acceptCookieConsent(page);
    if (consentResult === 1) {
      logger.silly(`[${config.name}] Cookie consent accepted successfully.`);
      console.log(`[${config.name}] Cookie consent accepted successfully.`);
      consentState.handled = true;
    } else {
      throw new Error(
        `[${config.name}] Cookie consent could not be accepted (Code: ${consentResult}).`
      );
    }
  }
  console.timeEnd(`⏳ accept cookie consent`);
}
