import { handleAdCheck } from './handle-ad-check.js';

export async function handler(job) {
  const jobName = job.name;

  switch (jobName) {
    case 'check':
      return await handleAdCheck(job.data);

    default:
      // Handle unknown job types
      console.error(`Unknown job type: ${jobName}`, { jobId: job.id });
      return {
        id: job.id,
        error: `Unknown job type: ${jobName}`,
        timestamp: new Date().toISOString(),
      };
  }
}
