import { groupAdsByResponsive, logger } from '../utils/index.mjs';
import {
  setupPage,
  injectPopupBlocker,
  navigateAndHandleConsent,
  waitForTcfApiWithRetry,
  preparePageForChecks,
} from '../preparation/index.mjs';
import { runAdChecks } from './run-ad-checks.mjs';

export async function runChecksForOneDevice(
  browser,
  url,
  deviceName,
  config,
  activeLayers,
  consentState
) {
  const page = await browser.newPage();
  const startTime = performance.now();

  try {
    // Set up page with event handlers and permissions
    await setupPage(page, config, url, browser);

    // Inject popup blocker before page loads
    await injectPopupBlocker(page);

    // Navigate to URL and handle cookie consent
    await navigateAndHandleConsent(page, url, config, consentState);

    // Wait for TCF API confirmation with retry logic
    await waitForTcfApiWithRetry(page, config);

    // Prepare page for checks: scroll and extract QMN data
    const qmnData = await preparePageForChecks(page, config);

    // we group the ads by responsive type (desktop/mobile)
    let adsByResponsive = {};
    adsByResponsive = groupAdsByResponsive(qmnData.config);

    console.log(
      `
      🗂️[${config.name}] [GROUP ADS BY RESPONSIVE] Ads by responsive:`,
      JSON.stringify(adsByResponsive, null, 2)
    );
    const responsiveKey = config.deviceType;
    const adsToCheck = adsByResponsive[responsiveKey] || [];
    logger.silly(
      `[${deviceName}] Check ${adsToCheck.length} ads for responsive type '${responsiveKey}'.`
    );
    console.log(
      `🗂️[${deviceName}] [GROUP ADS BY RESPONSIVE] Check ${adsToCheck.length} ads for responsive type '${responsiveKey}'.`
    );

    // Collect ads that should NOT be rendered on this device (wrong device ads)
    const wrongDeviceAds = Object.entries(adsByResponsive)
      .filter(([key]) => key !== responsiveKey)
      .flatMap(([key, ads]) => ads.map((ad) => ({ ...ad, expectedDevice: key })));

    logger.silly(
      `[${deviceName}] Found ${wrongDeviceAds.length} ads that should NOT be rendered on '${responsiveKey}'.`
    );

    // we run the checks for each layer (placement, visibility) and count the issues.
    const { issues, issueCount, iframeSources } = await runAdChecks(
      page,
      adsToCheck,
      wrongDeviceAds,
      deviceName,
      responsiveKey,
      activeLayers
    );

    const endTime = performance.now();
    const durationMs = endTime - startTime;

    return {
      deviceName,
      responsiveKey,
      issues,
      issueCount,
      error: null,
      adsByResponsive,
      iframeSources,
      durationMs,
      qmnData,
    };
  } catch (e) {
    logger.silly(`❌ Error with device ${deviceName}:`, e.message);
    console.log(`❌ Error with device ${deviceName}:`, e.message);
    return { deviceName, error: e.message, issues: {}, issueCount: 1 };
  } finally {
    await page.close();
  }
}
