import pushIssue from './push-issue.mjs';

/**
 * Process "Not within page bounds" visibility issue
 */
export function processNotWithinPageBounds(
  ad,
  result,
  responsiveKey,
  visibilityDetails,
  reason,
  issues
) {
  if (
    reason === 'Not within page bounds' ||
    visibilityDetails.isWithinPageBounds === false
  ) {
    pushIssue(
      {
        name: 'Not within PageBound',
        description: 'Ad not within the page',
        severity: 'RED',
        step: 'Check Visibility',
        device: result.deviceName,
        responsive: responsiveKey,
        adId: ad.id,
        adType: ad.type,
        visibilityDetails,
      },
      issues
    );
    return true;
  }
  return false;
}
