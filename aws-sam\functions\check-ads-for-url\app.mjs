import chromium from '@sparticuz/chromium';
import puppeteer from 'puppeteer-core';
import { checkAdsForUrl } from './main/index.mjs';

export const lambdaHandler = async (event) => {
  try {
    const url = event.url;
    const articleId = event.articleId;
    const layers = event.layers;

    console.log(`------------------------`);
    console.log('Received event:');
    console.log(`🔗 URL: ${url}`);
    console.log(`🆔 Article ID: ${articleId}`);
    console.log(`🎚️ layer: ${layers}`);
    console.log(`------------------------`);

    if (!url) {
      return { success: false, error: 'Missing parameter: url' };
    }

    if (!articleId) {
      return { success: false, error: 'Missing parameter: articleId' };
    }

    const browser = await puppeteer.launch({
      args: [
        ...chromium.args,
        '--disable-notifications',
        '--deny-permission-prompts',
      ],
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
      protocolTimeout: 360000,
    });

    const result = await checkAdsForUrl(browser, { url, articleId, layers });

    return { success: true, result };
  } catch (e) {
    return {
      success: false,
      error: e.message,
    };
  }
};
