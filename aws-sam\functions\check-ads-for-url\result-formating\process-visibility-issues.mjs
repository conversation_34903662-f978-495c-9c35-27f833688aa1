import { processNoContainerDiv } from './process-no-container-div.mjs';
import { processIframeMissing } from './process-iframe-missing.mjs';
import { processElementNotFound } from './process-element-not-found.mjs';
import { processHiddenByCss } from './process-hidden-by-css.mjs';
import { processNotWithinPageBounds } from './process-not-within-page-bounds.mjs';
import { processPartiallyOutOfViewport } from './process-partially-out-of-viewport.mjs';
import { processOccluded } from './process-occluded.mjs';

/**
 * Processes visibility issues from device results
 * @param {Object} result - Device result object
 * @param {string} responsiveKey - Responsive type (desktop/mobile)
 * @param {Array} issues - Issues array to push to
 */
export function processVisibilityIssues(result, responsiveKey, issues) {
  if (
    !result.issues ||
    !result.issues.visibility ||
    !result.issues.visibility[responsiveKey]
  ) {
    return;
  }

  for (const ad of result.issues.visibility[responsiveKey]) {
    const visibilityDetails = ad.visibilityDetails || {};
    const vis =
      typeof visibilityDetails.visibilityPercentage === 'number'
        ? visibilityDetails.visibilityPercentage
        : null;
    const reason = visibilityDetails.reason || '';

    // Check for specific error reasons
    if (
      processNoContainerDiv(
        ad,
        result,
        responsiveKey,
        visibilityDetails,
        reason,
        issues
      )
    )
      continue;
    if (
      processIframeMissing(
        ad,
        result,
        responsiveKey,
        visibilityDetails,
        reason,
        issues
      )
    )
      continue;
    if (
      processElementNotFound(
        ad,
        result,
        responsiveKey,
        visibilityDetails,
        reason,
        issues
      )
    )
      continue;
    if (
      processHiddenByCss(
        ad,
        result,
        responsiveKey,
        visibilityDetails,
        reason,
        issues
      )
    )
      continue;
    if (
      processNotWithinPageBounds(
        ad,
        result,
        responsiveKey,
        visibilityDetails,
        reason,
        issues
      )
    )
      continue;
    if (
      processPartiallyOutOfViewport(
        ad,
        result,
        responsiveKey,
        visibilityDetails,
        vis,
        issues
      )
    )
      continue;
    if (
      processOccluded(ad, result, responsiveKey, visibilityDetails, vis, issues)
    )
      continue;
  }
}
