/**
 * Pushes a normalized issue object into the provided issues array.
 *
 * @param {string} partial.issue - Short machine-friendly issue identifier.
 * @param {string|null} [partial.description] - Human-readable description.
 * @param {string|null} [partial.visibilityPercentage] - Visibility percentage string (e.g. "50%").
 * @param {number|null} [partial.visibility] - Visibility as a number (0-100).
 * @param {'RED'|'ORANGE'|'YELLOW'|'GREEN'} partial.severity - Severity level.
 * @param {string} partial.step - Processing step where the issue was detected (e.g. 'Page Opening').
 * @returns {void}
 */
export default function pushIssue(partial, issues) {
  issues.push({
    name: partial.name,
    description: partial.description || null,
    visibilityPercentage: partial.visibilityPercentage || null,
    severity: partial.severity,
    step: partial.step,
    context: {
      device: partial.device || null,
      responsive: partial.responsive || null,
      adId: partial.adId || null,
      adType: partial.adType || null,
      visibility:
        typeof partial.visibility === 'number' ? partial.visibility : null,
      occluders: partial.occluders || undefined,
      debugInfo: partial.visibilityDetails?.debugInfo || undefined,
    },
  });
}
