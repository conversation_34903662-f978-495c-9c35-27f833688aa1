import { logger } from '../utils/logger.mjs';
import { getPageScrollInfo } from './get-page-scroll-info.mjs';
import { findSidebarVisibilityPosition } from './find-sidebar-visibility-position.mjs';

/**
 * Handles sitebar-specific scroll visibility logic
 * @param {Object} page - Puppeteer page instance
 * @param {Object} parentContainerHandle - Parent container element handle
 * @param {Object} iframeHandle - Iframe element handle
 * @param {Object} ad - Ad object
 * @param {string} deviceName - Device name for logging
 * @returns {Promise<Object>} Object with updatedIframeHandle (may be same or new if iframe was replaced)
 */
export async function handleSitebar(
  page,
  parentContainerHandle,
  iframeHandle,
  ad,
  deviceName
) {
  console.time(`⏳ wait for scroll sitebar`);
  logger.silly(
    `### ➡️ [${deviceName}] Specific logic for 'sitebar' is applied.`
  );
  console.log(
    `### ➡️ [${deviceName}] [VISIBILITY] - Specific logic for 'sitebar' is applied.`
  );

  let isContainerVisibleByCss = await parentContainerHandle.evaluate((el) => {
    return el.checkVisibility({
      checkOpacity: true,
      checkVisibilityCSS: true,
    });
  });

  logger.silly(
    `### ➡️ [${deviceName}] Initial CSS visibility check for Sitebar container ad ${ad.id}: ${isContainerVisibleByCss}`
  );
  console.log(
    `### ➡️ [${deviceName}] [VISIBILITY] - Initial CSS visibility check for Sitebar container ad ${ad.id}: ${isContainerVisibleByCss}`
  );

  if (isContainerVisibleByCss) {
    logger.silly(
      `### ➡️ [${deviceName}] Sitebar container for ad ${ad.id} is already visible via CSS.`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - Sitebar container for ad ${ad.id} is already visible via CSS.`
    );
  } else {
    logger.silly(
      `### ➡️ [${deviceName}] Sitebar container for ad ${ad.id} is not visible via CSS. Trying to make it visible by scrolling.`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - Sitebar container for ad ${ad.id} is not visible via CSS. Trying to make it visible by scrolling.`
    );

    // Get current scroll information
    const scrollInfo = await getPageScrollInfo(page);
    const scrollAmount = 150; // Consistent scroll amount for systematic search
    const maxAttempts = Math.ceil(scrollInfo.maxScroll / scrollAmount) + 10; // Ensure we can cover the entire page

    logger.silly(
      `### ➡️ [${deviceName}] - Page scroll info: current=${scrollInfo.scrollTop}, max=${scrollInfo.maxScroll}, height=${scrollInfo.scrollHeight}`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - Searching for scroll position where sidebar becomes visible. Max attempts: ${maxAttempts}`
    );

    // systematic scrolling approach - first try scrolling down from current position
    let found = await findSidebarVisibilityPosition(
      page,
      parentContainerHandle,
      scrollInfo.scrollTop,
      scrollAmount,
      1, // direction: down
      Math.min(maxAttempts, 30),
      deviceName,
      ad.id,
      scrollInfo
    );

    // If not found scrolling down, try scrolling up from current position
    if (!found) {
      logger.silly(
        `### ➡️ [${deviceName}] - Sidebar not found scrolling down, trying scroll up from current position`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - Sidebar not found scrolling down, trying scroll up from current position`
      );

      // Reset to original position first ( avoid covering a space we already checked )
      await page.evaluate((scrollTop) => {
        window.scrollTo(0, scrollTop);
      }, scrollInfo.scrollTop);
      await new Promise((resolve) => setTimeout(resolve, 200));

      found = await findSidebarVisibilityPosition(
        page,
        parentContainerHandle,
        scrollInfo.scrollTop,
        scrollAmount,
        -1, // direction: up
        Math.min(maxAttempts, 30),
        deviceName,
        ad.id,
        scrollInfo
      );
    }

    // Final visibility check
    isContainerVisibleByCss = await parentContainerHandle.evaluate((el) => {
      if (typeof el.checkVisibility !== 'function') return false;
      return el.checkVisibility({
        checkOpacity: true,
        checkVisibilityCSS: true,
      });
    });

    if (isContainerVisibleByCss) {
      logger.silly(
        `### ➡️ [${deviceName}] - SUCCESS: Sitebar container is now visible via CSS after systematic scrolling.`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - SUCCESS: Sitebar container is now visible via CSS after systematic scrolling.`
      );
    } else {
      logger.silly(
        `### ➡️ [${deviceName}] - WARN: Could not find scroll position where sitebar becomes CSS visible after systematic search.`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - WARN: Could not find scroll position where sitebar becomes CSS visible after systematic search.`
      );
    }
  }

  await new Promise((resolve) => setTimeout(resolve, 500));

  // Re-verify iframe connection status after scrolling, as it might have been refreshed/replaced
  let updatedIframeHandle = iframeHandle;

  try {
    const isConnected = await iframeHandle.evaluate((el) => el.isConnected);
    if (!isConnected) {
      logger.silly(
        `### ➡️ [${deviceName}] - Iframe detached after scrolling. Attempting to re-acquire...`
      );
      const newIframeHandle = await parentContainerHandle.$(
        'iframe[id^="adspiritflash"]'
      );
      if (newIframeHandle) {
        await iframeHandle.dispose();
        updatedIframeHandle = newIframeHandle;
        logger.silly(
          `### ➡️ [${deviceName}] - Successfully re-acquired iframe handle after scrolling`
        );
      } else {
        logger.silly(`### ➡️ [${deviceName}] - Could not re-acquire iframe.`);
      }
    }
  } catch (error) {
    logger.silly(
      `### ➡️ [${deviceName}] - Error checking iframe connection: ${error.message}`
    );
  }

  console.timeEnd(`⏳ wait for scroll sitebar`);

  return { updatedIframeHandle };
}
