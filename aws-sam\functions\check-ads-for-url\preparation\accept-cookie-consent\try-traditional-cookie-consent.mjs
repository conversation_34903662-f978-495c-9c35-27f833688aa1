import { logger } from '../../utils/logger.mjs';
import { waitForElementStable } from './wait-for-element-stable.mjs';
import { verifyAndClickElement } from './verify-and-click-element.mjs';
import { calculateClickPoints } from './calculate-click-points.mjs';
import { locateElementWithActivity } from './locate-element-with-activity.mjs';

// Configuration for cookie consent selectors
const COOKIE_CONSENT_CONFIGS = [
  {
    name: 'Borlab<PERSON>ie',
    containerSelectors: ['.brlbs-cmpnt-dialog-iab-tcf'],
    buttonSelectors: ['.brlbs-btn-accept-all'],
  },
  {
    name: 'sirdata',
    containerSelectors: ['.sd-cmp-HRl9s'],
    buttonSelectors: ['.sd-cmp-Jou6v'],
  },
  {
    name: 'OneTrust',
    containerSelectors: ['.ot-sdk-container'],
    buttonSelectors: ['#onetrust-accept-btn-handler'],
  },
  // Add more configurations here for other cookie consent providers
];

/**
 * Attempts to find and accept cookie consent using traditional DOM approach.
 * Tries multiple configurations and uses mouse/scroll activity to trigger lazy-loaded elements.
 */
export async function tryTraditionalCookieConsent(page) {
  logger.silly('Trying traditional cookie consent approach...');

  for (const config of COOKIE_CONSENT_CONFIGS) {
    logger.silly(`Checking for ${config.name}...`);

    // Try each container selector
    for (const containerSelector of config.containerSelectors) {
      try {
        const container = await locateElementWithActivity(
          page,
          containerSelector,
          false,
          false,
          {
            elementName: 'Container',
          }
        );

        if (!container) continue;

        logger.silly(`Found container: ${containerSelector}`);

        // Try each button selector within the container
        for (const buttonSelector of config.buttonSelectors) {
          try {
            const button = await container.waitForSelector(buttonSelector);
            if (!button) continue;

            logger.silly(`Found button: ${buttonSelector}`);

            // Wait for animation to complete - check bounding box stability
            const stableBox = await waitForElementStable(button);

            // If element never stabilized or is outside viewport, skip this button
            if (!stableBox) {
              logger.silly(
                'Button did not stabilize within timeout, skipping...'
              );
              continue;
            }

            // Get button's bounding box
            const box = stableBox;
            if (!box) {
              logger.silly('Button has no bounding box');
              continue;
            }

            // Verify box is within viewport bounds
            if (box.y < 0 || box.x < 0) {
              logger.silly(
                `Button is outside viewport (x=${box.x}, y=${box.y}), skipping...`
              );
              continue;
            }

            // Calculate click points
            const clickPoints = calculateClickPoints(box);

            // Try clicking at various points
            for (const point of clickPoints) {
              const clicked = await verifyAndClickElement(
                page,
                button,
                point,
                config.name
              );
              if (clicked) {
                return 1;
              }
            }

            logger.silly('Unable to verify safe click coordinate');
            return 0;
          } catch (err) {
            logger.silly(`Error with button ${buttonSelector}:`, err.message);
          }
        }
      } catch (err) {
        logger.silly(`Error with container ${containerSelector}:`, err.message);
      }
    }
  }

  return -1; // Not found
}
