export default function calculateProblemPercentage(
  issues = [],
  adsByResponsive = {}
) {
  // Calculate total number of ads across all devices
  let totalAds = 0;
  for (const [, ads] of Object.entries(adsByResponsive)) {
    totalAds += ads.length;
  }

  if (totalAds === 0) {
    return 0;
  }

  const adsWithIssues = new Set();

  issues.forEach((issue) => {
    // Only consider issues with severity RED, ORANGE, or YELLOW as issues
    if (
      issue.severity &&
      ['RED', 'ORANGE', 'YELLOW'].includes(issue.severity) &&
      issue.context &&
      issue.context.device &&
      issue.context.responsive &&
      issue.context.adId
    ) {
      // Create unique identifier: device-responsive-adId
      const adIdentifier = `${issue.context.device}-${issue.context.responsive}-${issue.context.adId}`;
      adsWithIssues.add(adIdentifier);
    }
  });

  const problemPercentage = (adsWithIssues.size / totalAds) * 100;
  return Math.round(problemPercentage * 100) / 100;
}
