import pushIssue from './push-issue.mjs';

/**
 * Process "Iframe missing" visibility issue
 */
export function processIframeMissing(
  ad,
  result,
  responsiveKey,
  visibilityDetails,
  reason,
  issues
) {
  if (reason.includes('Iframe element not found')) {
    pushIssue(
      {
        name: 'iframe missing',
        description: 'Cannot check the Ad if no iframe',
        severity: 'RED',
        step: 'Check Visibility',
        device: result.deviceName,
        responsive: responsiveKey,
        adId: ad.id,
        adType: ad.type,
        visibilityDetails,
      },
      issues
    );
    return true;
  }
  return false;
}
