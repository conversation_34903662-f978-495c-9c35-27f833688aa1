import pushIssue from './push-issue.mjs';

/**
 * Processes wrong-device issues from device results
 * @param {Object} result - Device result object
 * @param {string} responsiveKey - Responsive type (desktop/mobile)
 * @param {Array} issues - Issues array to push to
 */
export function processWrongDeviceIssues(result, responsiveKey, issues) {
  if (
    !result.issues ||
    !result.issues.wrongDevice ||
    !result.issues.wrongDevice[responsiveKey]
  ) {
    return;
  }

  for (const ad of result.issues.wrongDevice[responsiveKey]) {
    const details = ad.wrongDeviceDetails || {};
    const hasIframe = details.iframeFound;
    const expectedOn = details.expectedOn;

    if (hasIframe) {
      // Fully rendered on wrong device
      pushIssue(
        {
          name: 'Ad rendered on wrong device',
          description: `Ad configured for ${expectedOn} is fully rendered on ${responsiveKey}`,
          severity: 'RED',
          step: 'Check Visibility',
          device: result.deviceName,
          responsive: responsiveKey,
          adId: ad.id,
          adType: ad.type,
        },
        issues
      );
    } else {
      // Container exists but no iframe
      pushIssue(
        {
          name: 'Ad container on wrong device',
          description: `Ad container for ${expectedOn} found on ${responsiveKey} (no iframe)`,
          severity: 'RED',
          step: 'Check Visibility',
          device: result.deviceName,
          responsive: responsiveKey,
          adId: ad.id,
          adType: ad.type,
        },
        issues
      );
    }
  }
}
