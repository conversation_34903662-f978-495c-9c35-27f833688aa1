/**
 * Calculates ad summary by device and type
 * @param {Array} deviceResults - Array of device result objects
 * @param {Array} issues - Array of issues found during checks
 * @returns {Object} Ad summary grouped by device and type
 */
export function calculateAdSummary(deviceResults, issues) {
  const summary = {};

  // Create a map of issues by device, responsive, adId, and adType
  const issueMap = new Map();

  issues.forEach((issue) => {
    if (
      issue.context &&
      issue.context.device &&
      issue.context.responsive &&
      issue.context.adId &&
      issue.context.adType
    ) {
      const key = `${issue.context.device}-${issue.context.responsive}-${issue.context.adId}`;

      if (!issueMap.has(key)) {
        issueMap.set(key, {
          device: issue.context.device,
          responsive: issue.context.responsive,
          adId: issue.context.adId,
          adType: issue.context.adType,
          issueCount: 0,
        });
      }

      // Increment issue count for this ad
      issueMap.get(key).issueCount++;
    }
  });

  // Process each device result
  deviceResults.forEach((result) => {
    const responsiveKey = result.responsiveKey;

    if (!responsiveKey || result.error) {
      return;
    }

    // Initialize the responsive key in summary if not exists
    if (!summary[responsiveKey]) {
      summary[responsiveKey] = {
        total: 0,
        byType: {},
      };
    }

    // Get ads for this device
    const adsToCheck = result.adsByResponsive?.[responsiveKey] || [];
    summary[responsiveKey].total = adsToCheck.length;

    // Group by ad type
    adsToCheck.forEach((ad) => {
      const adType = ad.type;

      if (!summary[responsiveKey].byType[adType]) {
        summary[responsiveKey].byType[adType] = {
          total: 0,
          passed: 0,
          issues: 0,
          ids: {
            passed: [],
            withIssues: [],
          },
        };
      }

      summary[responsiveKey].byType[adType].total++;

      // Check if this ad has issues
      const issueKey = `${result.deviceName}-${responsiveKey}-${ad.id}`;
      const adIssueInfo = issueMap.get(issueKey);

      if (adIssueInfo && adIssueInfo.issueCount > 0) {
        // Ad has issues
        summary[responsiveKey].byType[adType].issues += adIssueInfo.issueCount;
        summary[responsiveKey].byType[adType].ids.withIssues.push(ad.id);
      } else {
        // Ad passed (no issues)
        summary[responsiveKey].byType[adType].passed++;
        summary[responsiveKey].byType[adType].ids.passed.push(ad.id);
      }
    });
  });

  return summary;
}
