import pushIssue from './push-issue.mjs';

/**
 * Processes placement issues from device results
 * @param {Object} result - Device result object
 * @param {string} responsiveKey - Responsive type (desktop/mobile)
 * @param {Array} issues - Issues array to push to
 */
export function processPlacementIssues(result, responsiveKey, issues) {
  if (
    result.issues &&
    result.issues.placement &&
    result.issues.placement[responsiveKey]
  ) {
    for (const ad of result.issues.placement[responsiveKey]) {
      pushIssue(
        {
          name: 'qmn div container not found',
          description: 'Cannot check the Ad if no qmn[div] container',
          severity: 'RED',
          step: 'Check Existence',
          device: result.deviceName,
          responsive: responsiveKey,
          adId: ad.id,
          adType: ad.type,
        },
        issues
      );
    }
  }
}
