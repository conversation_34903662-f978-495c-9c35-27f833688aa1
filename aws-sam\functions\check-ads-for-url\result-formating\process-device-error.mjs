import pushIssue from './push-issue.mjs';

/**
 * Processes device-level errors and maps them to issues
 * @param {Object} result - Device result object
 * @param {string} responsiveKey - Responsive type (desktop/mobile)
 * @param {Array} issues - Issues array to push to
 */
export function processDeviceError(result, responsiveKey, issues) {
  const msg = String(result.error);

  if (msg.includes('<PERSON>ie consent could not be accepted')) {
    pushIssue(
      {
        name: '<PERSON><PERSON> consent fails',
        description: null,
        severity: 'RED',
        step: 'Page Opening',
        device: result.deviceName,
        responsive: responsiveKey,
      },
      issues
    );
  } else if (msg.includes('TCF validation failed')) {
    pushIssue(
      {
        name: 'TCF Validation fails',
        description: null,
        severity: 'RED',
        step: 'Page Opening',
        device: result.deviceName,
        responsive: responsiveKey,
      },
      issues
    );
  } else if (msg.includes('Could not retrieve window.qmn.config object')) {
    pushIssue(
      {
        name: 'No qmn object on website',
        description: null,
        severity: 'RED',
        step: 'Check Validity',
        device: result.deviceName,
        responsive: responsiveKey,
      },
      issues
    );
  } else {
    pushIssue(
      {
        name: 'Device run failed',
        description: msg,
        severity: 'RED',
        step: 'Page Opening',
        device: result.deviceName,
        responsive: responsiveKey,
      },
      issues
    );
  }
}
