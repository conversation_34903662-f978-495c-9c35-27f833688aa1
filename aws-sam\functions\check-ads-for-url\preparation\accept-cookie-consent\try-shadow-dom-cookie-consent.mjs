import { logger } from '../../utils/logger.mjs';
import { verifyAndClickElement } from './verify-and-click-element.mjs';
import { calculateClickPoints } from './calculate-click-points.mjs';
import { locateElementWithActivity } from './locate-element-with-activity.mjs';

/**
 * Attempts to find and accept cookie consent using Shadow DOM approach.
 * Specifically targets elements within shadow roots and uses mouse/scroll activity to trigger lazy-loaded elements.
 */

export async function tryShadowDomCookieConsent(page) {
  logger.silly('Trying shadow DOM cookie consent approach...');

  let shadowHost = await locateElementWithActivity(page, '#cmpwrapper', {
    elementName: 'Consent shadow host',
  });

  if (!shadowHost) return -1;

  const shadowRootHandle = await shadowHost.evaluateHandle(
    (el) => el.shadowRoot
  );

  if (!shadowRootHandle) return -1;

  // Try to find the button and wait for it to be available
  const acceptButtonHandle = await page.waitForFunction(
    (shadowRoot) => shadowRoot.querySelector('.cmptxt_btn_yes'),
    { timeout: 5000 },
    shadowRootHandle
  );

  if (!acceptButtonHandle) {
    logger.silly('❌ Consent button not found in shadow root');
    return -1;
  }
  const button = acceptButtonHandle.asElement();

  if (button) {
    const box = await button.boundingBox();

    if (!box) {
      logger.silly('❌ Consent button has no bounding box');
      return -1;
    }

    const clickPoints = calculateClickPoints(box);

    for (const point of clickPoints) {
      const clicked = await verifyAndClickElement(
        page,
        button,
        point,
        'Shadow DOM consent',
        shadowRootHandle
      );
      if (clicked) {
        return 1;
      }
    }

    logger.silly(
      '❌ Unable to verify a safe click coordinate for the consent button'
    );
    return -1;
  } else {
    logger.silly('❌ Consent button not found as element');
    return -1;
  }
}
