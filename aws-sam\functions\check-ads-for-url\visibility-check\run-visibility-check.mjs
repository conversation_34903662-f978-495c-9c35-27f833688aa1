import { logger } from '../utils/index.mjs';
import { discoverIframeForAd } from './discover-iframe-for-ad.mjs';
import { handleSitebar } from './handle-sitebar.mjs';
import { handleFloorad } from './handle-floorad.mjs';
import { scrollAdIntoView } from './scroll-ad-into-view.mjs';
import { evaluateAdVisibility } from './evaluate-ad-visibility.mjs';

export async function runVisibilityCheck(
  page,
  adsToCheck,
  wrongDeviceAds,
  deviceName,
  responsiveKey,
  issues
) {
  if (adsToCheck.length === 0 && wrongDeviceAds.length === 0) {
    logger.silly(
      `### ➡️ [${deviceName}] SUCCESS [VISIBILITY]: No ads to check for this layer.`
    );

    return { notVisibleAdsCount: 0, wrongDeviceAdsCount: 0, iframeSources: [] };
  }

  const notVisibleAds = [];
  const iframeSrcList = []; // Array to collect iframe src attributes
  logger.silly(
    `### ➡️ [${deviceName}] [VISIBILITY] Starting individual visibility check for ${adsToCheck.length} ads...`
  );

  for (const ad of adsToCheck) {
    // Discover iframe for this ad
    const discoveryResult = await discoverIframeForAd(page, ad, deviceName);

    if (discoveryResult.error) {
      const details = {
        isTrulyVisible: false,
        reason: discoveryResult.error,
      };
      notVisibleAds.push({ ...ad, visibilityDetails: details });
      logger.silly(
        `### ➡️ [${deviceName}] - Ad ID ${ad.id}: ${discoveryResult.error}`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - Ad ID ${ad.id}: ${discoveryResult.error}`
      );
      continue;
    }

    let { iframeHandle, parentContainerHandle, adSelector, iframeSrc } =
      discoveryResult;

    // Collect iframe source
    if (iframeSrc) {
      iframeSrcList.push({ adId: ad.id, type: ad.type, src: iframeSrc });
    }

    // --- Ad-Type-Specific Visibility Logic ---
    logger.silly(
      `### ➡️ [${deviceName}] - Applying visibility logic for ad type: ${ad.type}`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - Applying visibility logic for ad type: ${ad.type}`
    );

    let cleanupFloorad = null;

    switch (ad.type) {
      case 'floorad': {
        cleanupFloorad = await handleFloorad(page, deviceName);
        break;
      }

      case 'sitebar': {
        const sitebarResult = await handleSitebar(
          page,
          parentContainerHandle,
          iframeHandle,
          ad,
          deviceName
        );
        iframeHandle = sitebarResult.updatedIframeHandle;
        break;
      }

      case 'intext':
      case 'prospekt':
      case 'hpa':
      default: // Standard logic for known and unknown types
        await scrollAdIntoView(iframeHandle, deviceName);
        break;
    }

    // Evaluate visibility
    const visibilityIssue = await evaluateAdVisibility(
      page,
      adSelector,
      ad,
      deviceName
    );

    if (visibilityIssue) {
      notVisibleAds.push(visibilityIssue);
    }

    // Close the Floorad ad if it was opened
    if (cleanupFloorad) {
      await cleanupFloorad();
    }

    if (parentContainerHandle) {
      await parentContainerHandle.dispose();
    }
    await iframeHandle.dispose(); // Clean up the iframe handle
  }

  if (notVisibleAds.length > 0) {
    if (!issues.visibility) issues.visibility = { desktop: [], mobile: [] };
    issues.visibility[responsiveKey] = notVisibleAds;
    logger.silly(
      `[${deviceName}] ERROR [Visibility]: ${notVisibleAds.length} of ${adsToCheck.length} ads not really visible.`
    );
    console.log(
      `[${deviceName}] ERROR [Visibility]: ${notVisibleAds.length} of ${adsToCheck.length} ads not really visible.`
    );
  } else {
    logger.silly(
      `[${deviceName}] SUCCESS [Visibility]: All ${adsToCheck.length} ads are visible.`
    );
    console.log(
      `[${deviceName}] SUCCESS [Visibility]: All ${adsToCheck.length} ads are visible.`
    );
  }

  // --- Wrong Device Ads Check ---
  const wrongDeviceAdsFound = [];

    for (const ad of wrongDeviceAds) {
      const result = await discoverIframeForAd(page, ad, deviceName, true);

      if (result.containerFound) {
        logger.silly(
          `### ➡️ [${deviceName}] [WRONG-DEVICE] ❌ Ad ${ad.id} (expected: ${ad.expectedDevice}) found on wrong device! (iframe: ${result.iframeFound})`
        );
        console.log(
          `### ➡️ [${deviceName}] [WRONG-DEVICE] ❌ Ad ${ad.id} (expected: ${ad.expectedDevice}) found on wrong device! (iframe: ${result.iframeFound})`
        );

        wrongDeviceAdsFound.push({
          ...ad,
          wrongDeviceDetails: {
            containerFound: result.containerFound,
            iframeFound: result.iframeFound,
            expectedOn: ad.expectedDevice,
          },
        });
      }
    }

    if (wrongDeviceAdsFound.length > 0) {
      if (!issues.wrongDevice) issues.wrongDevice = { desktop: [], mobile: [] };
      issues.wrongDevice[responsiveKey] = wrongDeviceAdsFound;
      console.log(
        `[${deviceName}] ERROR [Wrong-Device]: ${wrongDeviceAdsFound.length} ads rendered on wrong device.`
      );
    } else {
      console.log(
        `[${deviceName}] SUCCESS [Wrong-Device]: No wrong-device ads found.`
      );
    }
  }

  return {
    notVisibleAdsCount: notVisibleAds.length,
    wrongDeviceAdsCount: wrongDeviceAdsFound.length,
    iframeSources: iframeSrcList,
  };
}
