export const simulateScrollActivity = async (page, checkFn) => {
  const scrollStep = 500;
  const maxScrollAttempts = 3;

  for (let attempt = 0; attempt < maxScrollAttempts; attempt += 1) {
    const reachedEnd = await page.evaluate((step) => {
      const scroller = document.scrollingElement;
      const maxTop = scroller.scrollHeight - window.innerHeight;
      const nextTop = Math.min(scroller.scrollTop + step, maxTop);
      // Use smooth behavior to trigger lazy-loading that depends on scroll animation
      scroller.scrollTo({ top: nextTop, behavior: 'smooth' });
      return scroller.scrollTop + window.innerHeight >= scroller.scrollHeight;
    }, scrollStep);

    // Wait a bit longer to allow the smooth scroll animation to complete
    await new Promise((resolve) => setTimeout(resolve, 600));

    const result = await checkFn();
    if (result) return result;
    if (reachedEnd) break;
  }

  return null;
};
