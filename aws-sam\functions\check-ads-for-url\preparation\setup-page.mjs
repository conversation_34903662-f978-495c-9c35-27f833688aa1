import { logger } from '../utils/logger.mjs';

/**
 * Sets up a new browser page with event handlers and permissions
 * @param {Object} page - Puppeteer page instance
 * @param {Object} config - Device configuration with name, width, height
 * @param {string} url - URL to set permissions for
 * @param {Object} browser - Puppeteer browser instance
 */
export async function setupPage(page, config, url, browser) {
  // Block notification permission requests
  const context = browser.defaultBrowserContext();
  await context.overridePermissions(url, []);

  // Log browser console messages
  page.on('console', (msg) => {
    logger.silly(`[${config.name} BROWSER LOG]: ${msg.text()}`);
  });

  // Handle and dismiss browser dialogs (alerts, confirms, prompts)
  page.on('dialog', async (dialog) => {
    logger.silly(`[${config.name} BROWSER LOG]: ${dialog.message()}`);
    await dialog.dismiss();
  });
}
