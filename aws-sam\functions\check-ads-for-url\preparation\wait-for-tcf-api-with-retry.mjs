import { logger } from '../utils/logger.mjs';
import { simulateMouseActivity } from './simulate-mouse-activity.mjs';
import { validateTcfApi } from './validate-tcf-api.mjs';

/**
 * Waits for TCF API with retry logic and mouse simulation
 * @param {Object} page - Puppeteer page instance
 * @param {Object} config - Device configuration
 * @throws {Error} If TCF API validation fails after all attempts
 */
export async function waitForTcfApiWithRetry(page, config) {
  logger.silly(`[${config.name}] Wait for TCF API confirmation... ⏳ `);
  console.log(`[${config.name}] Wait for TCF API confirmation... ⏳ `);

  console.time(`⏳ wait for TCF API`);
  try {
    const maxTcfAttempts = 3;
    for (let attempt = 0; attempt < maxTcfAttempts; attempt += 1) {
      try {
        await validateTcfApi(page);
        logger.silly(
          `[${config.name}] TCF API confirmation received successfully. ✅ `
        );
        break;
      } catch (e) {
        const isTcfMissing = e?.message?.includes(
          '__tcfapi function not found on page within the timeout'
        );
        if (!isTcfMissing || attempt === maxTcfAttempts - 1) {
          throw e;
        }

        logger.silly(
          `[${config.name}] __tcfapi not detected, simulating mouse movement before retry...`
        );
        await simulateMouseActivity(page, async () =>
          page.evaluate(() => Boolean(window.__tcfapi))
        );
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
    }
  } catch (e) {
    logger.silly(
      `[${config.name}] Error waiting for TCF API: ${e.message} ❌ `
    );

    throw new Error(`[${config.name}] TCF validation failed: ${e.message}`);
  } finally {
    console.timeEnd(`⏳ wait for TCF API`);
  }
}
