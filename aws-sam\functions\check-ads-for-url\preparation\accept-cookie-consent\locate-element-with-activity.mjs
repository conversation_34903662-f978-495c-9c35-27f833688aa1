import { logger } from '../../utils/logger.mjs';
import { simulateMouseActivity } from '../simulate-mouse-activity.mjs';
import { simulateScrollActivity } from '../simulate-scroll-activity.mjs';
import { createLocator } from './locate-element.mjs';

/**
 * Attempts to locate an element using multiple strategies with fallbacks.
 * First tries direct location, then triggers mouse activity, then scroll activity.
 * This is useful for lazy-loaded elements that only appear after user interaction.
 */
export async function locateElementWithActivity(
  page,
  selector,
  activateMouseActivity = true,
  activateScrollActivity = true,
  options = {}
) {
  const { elementName = 'Element' } = options;

  // Create a locator function for this container
  const locatorFunction = createLocator(page, selector);

  // Try direct location first
  let element = await locatorFunction();

  // Try mouse activity if not found
  if (!element && activateMouseActivity) {
    logger.silly(
      `${elementName} not found initially, trying mouse activity...`
    );
    element = await simulateMouseActivity(page, locatorFunction);
  }

  // Try scroll activity if still not found
  if (!element && activateScrollActivity) {
    logger.silly(`${elementName} not found after mouse, trying scroll...`);
    element = await simulateScrollActivity(page, locatorFunction);
  }

  return element;
}
