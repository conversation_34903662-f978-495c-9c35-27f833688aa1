import pushIssue from './push-issue.mjs';

/**
 * Process "Hidden by CSS" visibility issue
 */
export function processHiddenByCss(
  ad,
  result,
  responsiveKey,
  visibilityDetails,
  reason,
  issues
) {
  if (
    reason === 'Hidden by CSS' ||
    visibilityDetails.isVisibleByCss === false
  ) {
    pushIssue(
      {
        name: 'Hidden by CSS',
        description: 'Ad hidden by CSS properties.',
        severity: 'RED',
        step: 'Check Visibility',
        device: result.deviceName,
        responsive: responsiveKey,
        adId: ad.id,
        adType: ad.type,
        visibilityDetails,
      },
      issues
    );
    return true;
  }
  return false;
}
