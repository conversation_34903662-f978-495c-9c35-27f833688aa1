/**
 * Calculates timing breakdown per device
 * @param {Array} deviceResults - Array of device result objects with timing info
 * @returns {Object} Timing breakdown by device
 */
export function calculateTimingBreakdown(deviceResults) {
  const breakdown = {};

  deviceResults.forEach((result) => {
    if (result.responsiveKey && result.durationMs !== undefined) {
      breakdown[result.responsiveKey] = {
        durationMs: Math.round(result.durationMs),
      };
    }
  });

  return breakdown;
}
