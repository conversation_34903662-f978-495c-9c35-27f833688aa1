import { logger } from '../utils/logger.mjs';

export const simulateMouseActivity = async (page, checkFn) => {
  if (!page?.mouse?.move) return null;

  const viewport = page.viewport();

  const width = viewport?.width ?? 800;
  const height = viewport?.height ?? 600;
  const positions = [
    { x: width * 0.5, y: height * 0.5 },
    { x: width * 0.5, y: height * 0.8 },
    { x: width * 0.8, y: height * 0.5 },
  ];

  for (const position of positions) {
    try {
      await page.mouse.move(position.x, position.y, { steps: 12 });
      await new Promise((resolve) => setTimeout(resolve, 150));
      if (!checkFn) continue;
      const result = await checkFn();
      if (result) return result;
    } catch (err) {
      logger.silly(`⚠️ Mouse move attempt failed: ${err.message}`);
    }
  }

  return null;
};
