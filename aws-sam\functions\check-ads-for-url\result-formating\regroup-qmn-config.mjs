/**
 * Regroups QMN data's config.adSlots by device/responsive type
 * @param {Object} qmnData - Original QMN data object (contains config and other properties)
 * @returns {Object} QMN data with config.adSlots regrouped by device
 */
export function regroupQmnConfigByDevice(qmnData) {
  if (!qmnData || !qmnData.config) {
    return null;
  }

  // Create a deep copy of the original qmnData
  const result = { ...qmnData, config: { ...qmnData.config } };

  // Regroup adSlots by device if they exist
  if (qmnData.config.adSlots && Array.isArray(qmnData.config.adSlots)) {
    const adSlotsByDevice = {
      desktop: [],
      mobile: [],
    };

    // Group adSlots by responsive type
    qmnData.config.adSlots.forEach((adSlot) => {
      const responsive = adSlot.responsive || 'desktop';

      if (adSlotsByDevice[responsive]) {
        adSlotsByDevice[responsive].push(adSlot);
      }
    });

    // Replace the adSlots array with the grouped object
    result.config.adSlots = adSlotsByDevice;
  }

  return result;
}
