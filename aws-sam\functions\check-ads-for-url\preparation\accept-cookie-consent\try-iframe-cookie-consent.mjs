import { logger } from '../../utils/logger.mjs';
import { handleIframeConsentButtons } from './handle-iframe-consent-buttons.mjs';

// Configuration for iframe-based cookie consent
const IFRAME_CONSENT_CONFIGS = [
  {
    name: 'Privacy Management (cdn.privacy-mgmt.com)',
    iframeSelectors: ['iframe[id^="sp_message_iframe_"]'],
    containerSelectors: ['.cmp-banner'],
    buttonSelectors: ['.accept-all'],
  },
];

/**
 * Attempts to find and accept cookie consent within iframes.
 * Targets consent dialogs rendered in iframe elements.
 */
export async function tryIframeCookieConsent(page) {
  logger.silly('Trying iframe cookie consent approach...');

  for (const config of IFRAME_CONSENT_CONFIGS) {
    logger.silly(`Checking for ${config.name}...`);

    // Try each iframe selector
    for (const iframeSelector of config.iframeSelectors) {
      try {
        logger.silly(`Looking for iframe: ${iframeSelector}`);

        // Wait for iframe to be attached and visible
        const iframeElement = await page.waitForSelector(iframeSelector, {
          timeout: 5000,
          state: 'attached',
        });

        if (!iframeElement) continue;

        logger.silly(`Found iframe: ${iframeSelector}`);

        // Get the content frame
        const frame = await iframeElement.contentFrame();

        if (!frame) {
          logger.silly('Unable to access iframe content');
          continue;
        }

        logger.silly('Successfully accessed iframe content');

        // Try to find container within iframe
        let container = null;
        for (const containerSelector of config.containerSelectors) {
          try {
            logger.silly(`Looking for container: ${containerSelector}`);
            container = await frame.waitForSelector(containerSelector, {
              timeout: 3000,
            });
            if (container) {
              logger.silly(`Found container: ${containerSelector}`);
              break;
            }
          } catch (err) {
            logger.silly(
              `Container ${containerSelector} not found:`,
              err.message
            );
          }
        }

        const buttonResult = await handleIframeConsentButtons({
          page,
          frame,
          container,
          config,
        });

        if (buttonResult !== null) {
          return buttonResult;
        }
      } catch (err) {
        logger.silly(`Error with iframe ${iframeSelector}:`, err.message);
      }
    }
  }

  return -1; // Not found
}
