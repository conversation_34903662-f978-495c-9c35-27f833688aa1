import { viewports } from '../utils/index.mjs';
import { runChecksForOneDevice } from './run-checks-for-one-device.mjs';

/**
 * Runs checks across all configured device viewports
 * @param {Object} browser - Puppeteer browser instance
 * @param {string} url - URL to check
 * @param {Array} activeLayers - Array of active check layers (placement, visibility)
 * @returns {Promise<Array>} Array of device result objects
 */
export async function runChecksForAllDevices(browser, url, activeLayers) {
  const consentState = { handled: false }; // Track if consent has been handled
  const deviceResults = [];

  // Run checks sequentially to handle consent state correctly
  for (const [deviceName, config] of Object.entries(viewports)) {
    const result = await runChecksForOneDevice(
      browser,
      url,
      deviceName,
      config,
      activeLayers,
      consentState
    );
    deviceResults.push(result);
  }

  return deviceResults;
}
