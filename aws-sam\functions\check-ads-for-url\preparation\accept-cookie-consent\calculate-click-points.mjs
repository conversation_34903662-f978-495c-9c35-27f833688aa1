/**
 * Calculates multiple click points within a bounding box for safe element interaction.
 * Returns points at the center and corners with padding to avoid edge cases.
 */
export function calculateClickPoints(box, options = {}) {
  const { minPadding = 2, maxPadding = 8 } = options;

  const cornerPadding = Math.max(
    minPadding,
    Math.min(maxPadding, Math.floor(Math.min(box.width, box.height) / 4))
  );

  return [
    {
      label: 'center',
      x: box.x + box.width / 2,
      y: box.y + box.height / 2,
    },
    {
      label: 'top-left',
      x: box.x + cornerPadding,
      y: box.y + cornerPadding,
    },
    {
      label: 'top-right',
      x: box.x + Math.max(box.width - cornerPadding, cornerPadding),
      y: box.y + cornerPadding,
    },
    {
      label: 'bottom-left',
      x: box.x + cornerPadding,
      y: box.y + Math.max(box.height - cornerPadding, cornerPadding),
    },
    {
      label: 'bottom-right',
      x: box.x + Math.max(box.width - cornerPadding, cornerPadding),
      y: box.y + Math.max(box.height - cornerPadding, cornerPadding),
    },
  ];
}
