import { autoScrollToBottom } from './auto-scroll-to-bottom.mjs';
import { checkSelectors } from '../visibility-check/check-selectors.mjs';

/**
 * Prepares the page for ad checks by scrolling and extracting QMN config
 * @param {Object} page - Puppeteer page instance
 * @param {Object} config - Device configuration
 * @returns {Promise<Object>} QMN configuration object
 * @throws {Error} If QMN config cannot be retrieved
 */
export async function preparePageForChecks(page, config) {
  // A short wait can help stabilize the page before scrolling
  await new Promise((resolve) => setTimeout(resolve, 500));

  console.time(`⏳ auto scroll to bottom`);
  // Lazy load to ensure all content has been loaded before checking the ads
  await autoScrollToBottom(page);
  console.timeEnd(`⏳ auto scroll to bottom`);

  console.time(`⏳ check selectors`);
  // Extract the ads from the QMN object
  const qmnData = await checkSelectors(page);
  console.timeEnd(`⏳ check selectors`);

  if (!qmnData) {
    console.log(
      `🇦🇩 ❌ [${config.name}] [QMN CONFIG] Could not retrieve window.qmn object.`
    );
    throw new Error(`[${config.name}] Could not retrieve window.qmn object.`);
  }

  console.log(
    `
    🔧[${config.name}] [QMN CONFIG] QMN config:`,
    JSON.stringify(qmnData.config, null, 2)
  );

  return qmnData;
}
