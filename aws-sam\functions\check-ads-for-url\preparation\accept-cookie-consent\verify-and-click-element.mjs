import { logger } from '../../utils/logger.mjs';

/**
 * Verifies that an element is clickable at specific coordinates and clicks it if verification succeeds.
 * Uses elementsFromPoint to ensure the target element is the topmost element at the click coordinates.
 * Supports both regular DOM, Shadow DOM, and iframe contexts.
 *
 * @param {Page} page - The page object for clicking
 * @param {ElementHandle} element - The element to verify and click
 * @param {Object} point - The coordinates {x, y, label?}
 * @param {string} context - Context description for logging
 * @param {Object} shadowRootHandle - Shadow root handle (for shadow DOM elements)
 * @param {Frame} frame - Frame context (for iframe elements)
 */
export async function verifyAndClickElement(
  page,
  element,
  point,
  context = '',
  shadowRootHandle = null,
  frame = null
) {
  // Use frame context if provided, otherwise use page context
  const evaluationContext = frame || page;

  const verification = await evaluationContext.evaluate(
    (el, coords, shadowRoot) => {
      const root = shadowRoot || document;
      const stack = root.elementsFromPoint(coords.x, coords.y) || [];
      const candidate = stack[0] || null;
      return {
        matches: candidate === el,
        tag: candidate?.tagName?.toLowerCase(),
        className: candidate?.className,
        id: candidate?.id,
        stack: stack.map((el) => el.className).join(' > '),
      };
    },
    element,
    { x: point.x, y: point.y },
    shadowRootHandle
  );

  if (verification.matches) {
    await page.mouse.click(point.x, point.y);
    const pointLabel = point.label || `(${point.x}, ${point.y})`;
    const contextStr = context ? ` for ${context}` : '';
    logger.silly(`✅ Element clicked at ${pointLabel}${contextStr}`);
    return true;
  }

  const pointLabel = point.label || `(${point.x}, ${point.y})`;
  logger.silly(
    `Click at ${pointLabel} hit ${verification.tag || 'unknown'} ${
      verification.className || ''
    } ${verification.id ? `#${verification.id}` : ''}`.trim()
  );

  return false;
}
