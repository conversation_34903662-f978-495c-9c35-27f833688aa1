import pushIssue from './push-issue.mjs';

/**
 * Process "Element not found" visibility issue
 */
export function processElementNotFound(
  ad,
  result,
  responsiveKey,
  visibilityDetails,
  reason,
  issues
) {
  if (reason === 'Element not found') {
    pushIssue(
      {
        name: 'Bounding box not found',
        description: 'Element no found when trying to get boundingRect',
        severity: 'RED',
        step: 'Check Visibility',
        device: result.deviceName,
        responsive: responsiveKey,
        adId: ad.id,
        adType: ad.type,
        visibilityDetails,
      },
      issues
    );
    return true;
  }
  return false;
}
