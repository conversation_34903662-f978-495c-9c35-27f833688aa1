// Helper function to get current page scroll information
export async function getPageScrollInfo(page) {
  return await page.evaluate(() => {
    const scrollHeight = Math.max(
      document.body?.scrollHeight || 0,
      document.documentElement?.scrollHeight || 0
    );
    const clientHeight = window.innerHeight;

    return {
      scrollTop: window.scrollY,
      scrollHeight,
      clientHeight,
      maxScroll: Math.max(scrollHeight - clientHeight, 0),
    };
  });
}
