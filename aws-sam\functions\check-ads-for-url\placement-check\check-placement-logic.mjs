// --- Logic for Layer 1: Placement Check (executed in the browser context) ---
export const checkPlacementLogic = (ads) => {
  const missing = [];
  for (const ad of ads) {
    let adContainer;
    if (ad.type === 'watchbetter') {
      adContainer = document.getElementById(ad.id);
    } else {
      adContainer = document.getElementById(`qmn${ad.id}`);
    }
    if (!adContainer) {
      missing.push(ad);
    }
  }
  return missing;
};
