import { logger } from '../utils/logger.mjs';

/**
 * Handles floorad ad lifecycle - opens the ad and returns a cleanup function to close it
 * @param {Object} page - Puppeteer page instance
 * @param {string} deviceName - Device name for logging
 * @returns {Promise<Function>} Async cleanup function to close the ad
 */
export async function handleFloorad(page, deviceName) {
  console.time(`⏳ wait for floorad button`);
  logger.silly(
    `### ➡️ [${deviceName}] Specific logic for 'floorad' is applied.`
  );
  console.log(
    `### ➡️ [${deviceName}] [VISIBILITY] - Specific logic for 'floorad' is applied.`
  );

  const floorAdOpenButtonSelector = 'div[id^="flup-adspiritflash"]';
  const floorAdCloseButtonSelector = 'div[id^="fldown-adspiritflash"]';

  const floorAdOpenButtonHandle = await page.$(floorAdOpenButtonSelector);
  let closeButtonHandle = null;

  if (floorAdOpenButtonHandle) {
    logger.silly(
      `### ➡️ [${deviceName}] Floorad open button found (${floorAdOpenButtonSelector}). Click to open the ad.`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - Floorad open button found (${floorAdOpenButtonSelector}). Click to open the ad.`
    );
    await floorAdOpenButtonHandle.click();
    await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for animation

    // Now get the close button for later use
    closeButtonHandle = await page.$(floorAdCloseButtonSelector);

    // Clean up open button handle
    await floorAdOpenButtonHandle.dispose();
  } else {
    logger.silly(
      `### ➡️ [${deviceName}] WARN: Floorad open button (${floorAdOpenButtonSelector}) not found.`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - WARN: Floorad open button (${floorAdOpenButtonSelector}) not found.`
    );
  }

  console.timeEnd(`⏳ wait for floorad button`);

  // Return cleanup function
  return async () => {
    if (!closeButtonHandle) return;

    logger.silly(
      `### ➡️ [${deviceName}] Close Floorad ad by clicking close button.`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] - Close Floorad ad by clicking close button.`
    );

    try {
      await closeButtonHandle.click();
      await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for animation
    } catch (e) {
      logger.silly(
        `### ➡️ [${deviceName}] WARN: Floorad close button could not be clicked (possibly already closed): ${e.message}`
      );
      console.log(
        `### ➡️ [${deviceName}] [VISIBILITY] - WARN: Floorad close button could not be clicked (possibly already closed): ${e.message}`
      );
    } finally {
      await closeButtonHandle.dispose();
    }
  };
}
