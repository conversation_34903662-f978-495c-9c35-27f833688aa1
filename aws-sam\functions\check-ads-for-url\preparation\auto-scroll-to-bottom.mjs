export const autoScrollToBottom = async (page) => {
  await page.evaluate(async () => {
    // Configuration
    const CONFIG = {
      scrollStepPx: 200, // Pixels to scroll per step
      stepDelayMs: 100, // Delay between scroll steps
      contentWaitMs: 500, // Time to wait for lazy-loaded content
      maxContentWaitAttempts: 5, // Max times to wait for new content at bottom
      bottomThresholdPx: 10, // Threshold to consider we've reached bottom (accounts for browser quirks)
    };

    const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

    // Always reset to the top so we sweep the entire page even if cookie flows scrolled us down.
    window.scrollTo({ top: 0, behavior: 'instant' });
    await sleep(CONFIG.contentWaitMs);

    const getScrollMetrics = () => {
      const pageHeight = Math.max(
        document.body?.scrollHeight || 0,
        document.documentElement?.scrollHeight || 0
      );
      const viewportHeight = window.innerHeight;
      const maxScrollY = Math.max(pageHeight - viewportHeight, 0);
      const currentY = window.scrollY;
      const distanceToBottom = Math.max(maxScrollY - currentY, 0);

      return {
        currentY,
        pageHeight,
        viewportHeight,
        maxScrollY,
        distanceToBottom,
        isAtBottom: distanceToBottom <= CONFIG.bottomThresholdPx,
      };
    };

    let lastPageHeight = 0;
    let contentWaitAttempts = 0;

    while (true) {
      const metrics = getScrollMetrics();

      // Check if we've reached the bottom
      if (metrics.isAtBottom) {
        await sleep(CONFIG.contentWaitMs);

        const newMetrics = getScrollMetrics();

        if (newMetrics.pageHeight === lastPageHeight) {
          contentWaitAttempts++;

          if (contentWaitAttempts >= CONFIG.maxContentWaitAttempts) {
            break;
          }
        } else {
          contentWaitAttempts = 0;
        }

        lastPageHeight = newMetrics.pageHeight;
        continue;
      }

      // Scroll one step towards the bottom
      const scrollAmount = Math.min(
        CONFIG.scrollStepPx,
        metrics.distanceToBottom
      );
      const targetY = metrics.currentY + scrollAmount;

      window.scrollTo(0, targetY);
      await sleep(CONFIG.stepDelayMs);

      // Update last known page height
      const newMetrics = getScrollMetrics();
      lastPageHeight = newMetrics.pageHeight;
    }
  });

  // Final wait to ensure all content is loaded
  await new Promise((resolve) => setTimeout(resolve, 5000));
};
