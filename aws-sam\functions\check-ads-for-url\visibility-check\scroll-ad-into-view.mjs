import { logger } from '../utils/logger.mjs';

/**
 * Scrolls an ad iframe into the center of the viewport
 * Used for standard ad types (intext, prospekt, hpa, default)
 * @param {Object} iframeHandle - Iframe element handle
 * @param {string} deviceName - Device name for logging
 */
export async function scrollAdIntoView(iframeHandle, deviceName) {
  console.time(`⏳ wait for scroll into view`);
  logger.silly(
    `### ➡️ [${deviceName}] - Standard logic (scrollIntoView) is applied.`
  );
  console.log(
    `### ➡️ [${deviceName}] [VISIBILITY] - Standard logic (scrollIntoView) is applied.`
  );

  // Scroll the element into the center of the viewport, to ensure it is checkable.
  await iframeHandle.evaluate((el) => {
    const rect = el.getBoundingClientRect();
    const viewportHeight =
      window.innerHeight || document.documentElement.clientHeight;
    // Calculate the target scroll position to place the element 200px above the center
    const targetY =
      rect.top + window.scrollY - viewportHeight / 2 + rect.height / 2;
    window.scrollTo({ top: targetY, behavior: 'instant' });
  });

  // A short wait can help stabilize the page before scrolling.
  await new Promise((resolve) => setTimeout(resolve, 500));
  console.timeEnd(`⏳ wait for scroll into view`);
}
